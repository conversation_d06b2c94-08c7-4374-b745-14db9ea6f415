import { useState, useRef, useEffect } from "react";
import { Search, BookOpen, Users, Target, Lightbulb } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { usePhilosophySuggestions } from "@/hooks/use-philosophy-graph";
import { SearchSuggestion } from "@/lib/types";

interface SearchInterfaceProps {
  onSearch: (topic: string) => void;
  isLoading: boolean;
}

export function SearchInterface({ onSearch, isLoading }: SearchInterfaceProps) {
  const [searchTopic, setSearchTopic] = useState("");
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [debouncedQuery, setDebouncedQuery] = useState("");
  const inputRef = useRef<HTMLInputElement>(null);
  
  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(searchTopic);
    }, 300);
    
    return () => clearTimeout(timer);
  }, [searchTopic]);

  const { data: suggestions = [] } = usePhilosophySuggestions(debouncedQuery);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchTopic.trim()) {
      onSearch(searchTopic.trim());
      setShowSuggestions(false);
    }
  };

  const handleSuggestionClick = (suggestion: SearchSuggestion) => {
    setSearchTopic(suggestion.name);
    onSearch(suggestion.name);
    setShowSuggestions(false);
  };

  const quickStartTopics = [
    { name: "存在主义", icon: Users },
    { name: "伦理学", icon: Target },
    { name: "意识", icon: Lightbulb },
    { name: "自由意志", icon: BookOpen }
  ];

  return (
    <div className="bg-linear-surface border-b border-linear-border-light">
      <div className="max-w-4xl mx-auto px-4 py-12">
        <div className="text-center mb-10">
          <h2 className="font-inter text-3xl font-semibold text-linear-text-primary mb-3 tracking-tight">
            探索哲学概念
          </h2>
          <p className="text-linear-text-secondary font-inter text-lg font-normal">
            输入任何哲学主题来生成交互式知识图谱
          </p>
        </div>
        
        <form onSubmit={handleSubmit} className="relative max-w-2xl mx-auto">
          <div className="relative">
            <Input
              ref={inputRef}
              type="text"
              placeholder="输入哲学主题（例如：存在主义、自由意志、德性伦理学）"
              value={searchTopic}
              onChange={(e) => setSearchTopic(e.target.value)}
              onFocus={() => setShowSuggestions(true)}
              onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
              className="w-full px-6 py-4 text-base font-inter bg-linear-surface border border-linear-border rounded-xl focus:border-linear-primary focus:ring-2 focus:ring-linear-primary/10 pr-16 shadow-linear-sm transition-all"
              disabled={isLoading}
            />
            <Button
              type="submit"
              disabled={!searchTopic.trim() || isLoading}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 btn-primary-linear p-3 rounded-lg"
            >
              <Search className="w-4 h-4" />
            </Button>
          </div>
          
          {/* Search Suggestions */}
          {showSuggestions && suggestions.length > 0 && (
            <div className="absolute top-full left-0 right-0 bg-linear-surface border border-linear-border rounded-lg mt-2 shadow-linear-lg z-10 animate-scale-in">
              {suggestions.map((suggestion, index) => (
                <div
                  key={index}
                  onClick={() => handleSuggestionClick(suggestion)}
                  className="px-4 py-3 hover:bg-linear-surface-secondary cursor-pointer border-b border-linear-border-light last:border-b-0 transition-colors"
                >
                  <div className="font-medium text-linear-text-primary">{suggestion.name}</div>
                  <div className="text-sm text-linear-text-secondary">{suggestion.description}</div>
                </div>
              ))}
            </div>
          )}
        </form>
        
        {/* Quick Start Topics */}
        <div className="mt-8 text-center">
          <p className="text-sm text-linear-text-secondary mb-4 font-inter font-medium">快速开始：</p>
          <div className="flex flex-wrap justify-center gap-3">
            {quickStartTopics.map((topic) => {
              const IconComponent = topic.icon;
              return (
                <Button
                  key={topic.name}
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setSearchTopic(topic.name);
                    onSearch(topic.name);
                  }}
                  disabled={isLoading}
                  className="btn-secondary-linear px-4 py-2 rounded-full text-sm font-inter hover:shadow-linear-sm transition-all"
                >
                  <IconComponent className="w-4 h-4 mr-2" />
                  {topic.name}
                </Button>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
}
