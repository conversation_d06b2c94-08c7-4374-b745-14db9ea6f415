import { useState, useRef, useEffect } from "react";
import { X, Book, Users, Tag, Move, Maximize2, Minimize2 } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { GraphNode } from "@/lib/types";

// Simple markdown to HTML converter for basic formatting
function markdownToHtml(text: string): string {
    return text
        // Headers
        .replace(/^## (.*$)/gm, '<h3 class="text-lg font-semibold text-linear-text-primary mb-3 mt-4 first:mt-0 font-inter">$1</h3>')
        // Bold text
        .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold text-linear-text-primary">$1</strong>')
        // List items
        .replace(/^- \*\*(.*?)\*\*：(.*$)/gm, '<div class="mb-2"><strong class="text-linear-text-primary">$1</strong>：$2</div>')
        // Line breaks
        .replace(/\n\n/g, '<br><br>')
        .replace(/\n/g, '<br>');
}

interface ConceptSidebarProps {
    node: GraphNode | null;
    isOpen: boolean;
    onClose: () => void;
    onConceptClick: (concept: string) => void;
}

export function ConceptSidebar({ node, isOpen, onClose, onConceptClick }: ConceptSidebarProps) {
    // 所有hooks必须在条件返回之前调用
    const [position, setPosition] = useState({ x: 100, y: 100 });
    const [size, setSize] = useState({ width: 480, height: 600 });
    const [isMaximized, setIsMaximized] = useState(false);
    const [isDragging, setIsDragging] = useState(false);
    const [isResizing, setIsResizing] = useState(false);
    const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
    const [initialPos, setInitialPos] = useState({ x: 0, y: 0 });
    const floatingRef = useRef<HTMLDivElement>(null);

    // 将依赖于 node 的计算移到条件检查之后

    // Initialize position when opening - 考虑导航栏高度
    useEffect(() => {
        if (isOpen && !isDragging) {
            const headerHeight = 80; // 导航栏高度
            const centerX = window.innerWidth / 2 - size.width / 2;
            const centerY = (window.innerHeight - headerHeight) / 2 - size.height / 2 + headerHeight;
            setPosition({ 
                x: Math.max(50, centerX), 
                y: Math.max(headerHeight + 20, centerY) // 确保不被导航栏遮挡
            });
        }
    }, [isOpen, size.width, size.height, isDragging]);

    // Handle dragging
    const handleMouseDown = (e: React.MouseEvent) => {
        if (e.target === e.currentTarget || (e.target as HTMLElement).dataset.draggable === 'true') {
            setIsDragging(true);
            setDragStart({ x: e.clientX, y: e.clientY });
            setInitialPos({ x: position.x, y: position.y });
            e.preventDefault();
        }
    };

    useEffect(() => {
        const handleMouseMove = (e: MouseEvent) => {
            if (isDragging) {
                const headerHeight = 80; // 导航栏高度
                const deltaX = e.clientX - dragStart.x;
                const deltaY = e.clientY - dragStart.y;
                const newX = Math.max(0, Math.min(window.innerWidth - size.width, initialPos.x + deltaX));
                const newY = Math.max(headerHeight, Math.min(window.innerHeight - size.height, initialPos.y + deltaY));
                setPosition({ x: newX, y: newY });
            } else if (isResizing) {
                const deltaX = e.clientX - dragStart.x;
                const deltaY = e.clientY - dragStart.y;
                const newWidth = Math.max(320, Math.min(window.innerWidth - position.x - 20, initialPos.x + deltaX));
                const newHeight = Math.max(400, Math.min(window.innerHeight - position.y - 20, initialPos.y + deltaY));
                setSize({ width: newWidth, height: newHeight });
            }
        };

        const handleMouseUp = () => {
            setIsDragging(false);
            setIsResizing(false);
        };

        if (isDragging || isResizing) {
            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('mouseup', handleMouseUp);
        }

        return () => {
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);
        };
    }, [isDragging, isResizing, dragStart, initialPos, position, size]);

    // 添加键盘支持并阻止背景滚动
    useEffect(() => {
        const handleKeyDown = (e: KeyboardEvent) => {
            if (e.key === 'Escape' && isOpen) {
                onClose();
            }
        };

        if (isOpen) {
            document.addEventListener('keydown', handleKeyDown);
            // 阻止背景滚动
            document.body.style.overflow = 'hidden';
        }

        return () => {
            document.removeEventListener('keydown', handleKeyDown);
            // 恢复背景滚动
            document.body.style.overflow = '';
        };
    }, [isOpen, onClose]);

    const toggleMaximize = () => {
        const headerHeight = 80; // 导航栏高度
        if (isMaximized) {
            setSize({ width: 480, height: 600 });
            setPosition({ x: 100, y: headerHeight + 20 });
        } else {
            setSize({ 
                width: window.innerWidth - 100, 
                height: window.innerHeight - headerHeight - 50 
            });
            setPosition({ x: 50, y: headerHeight + 25 });
        }
        setIsMaximized(!isMaximized);
    };

    // 条件检查移到所有hooks之后
    if (!node || !isOpen) return null;

    // 现在可以安全地访问 node 的属性
    const categoryColors = {
        core: "bg-philo-primary",
        related: "bg-philo-secondary",
        principle: "bg-philo-accent"
    };

    const categoryIcons = {
        core: Users,
        related: Tag,
        principle: Book
    };

    const IconComponent = categoryIcons[node.category] || Users;

    return (
        <>
            {/* 黑色蒙层 - 覆盖整个屏幕包括顶部导航栏 */}
            <div
                className="fixed inset-0 bg-black/40 z-40 animate-fade-in cursor-pointer"
                onClick={onClose}
                style={{
                    backdropFilter: 'blur(4px)'
                }}
            />

            {/* 浮层 */}
            <div
                ref={floatingRef}
                className="fixed bg-linear-surface floating-sidebar shadow-linear-xl z-50 rounded-lg border border-linear-border-light animate-scale-in"
                style={{
                    left: position.x,
                    top: position.y,
                    width: size.width,
                    height: size.height,
                    cursor: isDragging ? 'grabbing' : 'default'
                }}
                onMouseDown={handleMouseDown}
                onClick={(e) => e.stopPropagation()}
            >
                {/* Header with drag handle */}
                <div
                    className="flex items-center justify-between p-4 border-b border-linear-border-light bg-linear-surface-secondary rounded-t-lg cursor-grab active:cursor-grabbing select-none"
                    data-draggable="true"
                >
                    <div className="flex items-center space-x-3">
                        <div className={`w-8 h-8 ${categoryColors[node.category]} rounded-lg flex items-center justify-center shadow-linear-sm`}>
                            <IconComponent className="w-4 h-4 text-white" />
                        </div>
                        <div>
                            <h3 className="font-inter text-lg font-semibold text-linear-text-primary tracking-tight">
                                {node.name}
                            </h3>
                            <Badge variant="secondary" className="text-linear-text-secondary font-inter text-xs bg-linear-surface-secondary border-linear-border">
                                {node.category === 'core' ? '核心概念' :
                                    node.category === 'related' ? '相关思想' : '关键原理'}
                            </Badge>
                        </div>
                    </div>

                    <div className="flex items-center space-x-1">
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={toggleMaximize}
                            className="p-2 hover:bg-linear-surface-tertiary rounded transition-colors"
                            title={isMaximized ? "还原" : "最大化"}
                        >
                            {isMaximized ?
                                <Minimize2 className="w-4 h-4 text-linear-text-secondary" /> :
                                <Maximize2 className="w-4 h-4 text-linear-text-secondary" />
                            }
                        </Button>
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={onClose}
                            className="p-2 hover:bg-linear-surface-tertiary rounded transition-colors"
                            title="关闭"
                        >
                            <X className="w-4 h-4 text-linear-text-secondary" />
                        </Button>
                    </div>
                </div>
                <ScrollArea className="flex-1" style={{ height: size.height - 80 }}>
                    <div className="p-6">
                        {/* Content Area */}
                        <div>

                            <div className="space-y-6">
                                {/* Explanation */}
                                <section>
                                    <h4 className="font-inter font-semibold text-linear-text-primary mb-3">解释</h4>
                                    <div className="text-linear-text-secondary font-inter text-sm leading-relaxed">
                                        <div dangerouslySetInnerHTML={{ __html: markdownToHtml(node.definition) }} />
                                    </div>
                                </section>

                                {/* Key Philosophers */}
                                {node.keyPhilosophers.length > 0 && (
                                    <section>
                                        <h4 className="font-inter font-semibold text-linear-text-primary mb-4 flex items-center">
                                            <Users className="w-4 h-4 mr-2" />
                                            主要哲学家
                                        </h4>
                                        <div className="space-y-3">
                                            {node.keyPhilosophers.map((philosopher, index) => (
                                                <div key={index} className="flex items-start space-x-3 p-4 bg-linear-surface-secondary rounded-lg border border-linear-border-light">
                                                    <div className={`w-8 h-8 ${categoryColors[node.category]} rounded-lg flex items-center justify-center flex-shrink-0 shadow-linear-sm`}>
                                                        <span className="text-white text-xs font-bold">
                                                            {philosopher.initials}
                                                        </span>
                                                    </div>
                                                    <div>
                                                        <h5 className="font-medium text-linear-text-primary font-inter">{philosopher.name}</h5>
                                                        <p className="text-xs text-linear-text-secondary font-inter">{philosopher.description}</p>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </section>
                                )}

                                {/* Key Works */}
                                {node.keyWorks.length > 0 && (
                                    <section>
                                        <h4 className="font-inter font-semibold text-linear-text-primary mb-4 flex items-center">
                                            <Book className="w-4 h-4 mr-2" />
                                            重要著作
                                        </h4>
                                        <div className="space-y-2">
                                            {node.keyWorks.map((work, index) => (
                                                <div
                                                    key={index}
                                                    className="flex items-center space-x-3 p-3 hover:bg-linear-surface-secondary rounded-lg transition-colors cursor-pointer"
                                                >
                                                    <Book className={`w-4 h-4 ${node.category === 'core' ? 'text-philo-primary' :
                                                        node.category === 'related' ? 'text-philo-secondary' :
                                                            'text-philo-accent'
                                                        }`} />
                                                    <span className="text-sm text-linear-text-primary font-inter">{work}</span>
                                                </div>
                                            ))}
                                        </div>
                                    </section>
                                )}

                                {/* Related Concepts */}
                                {node.relatedConcepts.length > 0 && (
                                    <section>
                                        <h4 className="font-inter font-semibold text-linear-text-primary mb-4 flex items-center">
                                            <Tag className="w-4 h-4 mr-2" />
                                            相关概念
                                        </h4>
                                        <div className="flex flex-wrap gap-2">
                                            {node.relatedConcepts.map((concept, index) => (
                                                <Badge
                                                    key={index}
                                                    variant="outline"
                                                    className={`px-3 py-1 text-xs font-inter cursor-pointer transition-all hover:shadow-linear-sm ${
                                                        node.category === 'core' ? 'text-philo-primary border-philo-primary/20 hover:bg-philo-primary/10' :
                                                        node.category === 'related' ? 'text-philo-secondary border-philo-secondary/20 hover:bg-philo-secondary/10' :
                                                        'text-philo-accent border-philo-accent/20 hover:bg-philo-accent/10'
                                                    }`}
                                                    onClick={() => onConceptClick(concept)}
                                                >
                                                    {concept}
                                                </Badge>
                                            ))}
                                        </div>
                                    </section>
                                )}
                            </div>
                        </div>
                    </div>
                </ScrollArea>

                {/* Resize handle */}
                {!isMaximized && (
                    <div
                        className="absolute bottom-0 right-0 w-6 h-6 cursor-se-resize opacity-50 hover:opacity-100 transition-opacity flex items-center justify-center"
                        onMouseDown={(e) => {
                            setIsResizing(true);
                            setDragStart({ x: e.clientX, y: e.clientY });
                            setInitialPos({ x: size.width, y: size.height });
                            e.preventDefault();
                            e.stopPropagation();
                        }}
                    >
                        <svg width="12" height="12" viewBox="0 0 12 12" className="text-linear-text-tertiary">
                            <path d="M12 12L0 12L12 0Z" fill="currentColor" opacity="0.6" />
                            <path d="M8 12L12 8L12 12Z" fill="currentColor" />
                            <path d="M4 12L12 4L12 12Z" fill="currentColor" opacity="0.3" />
                        </svg>
                    </div>
                )}
            </div>
        </>
    );
}
