import { X, Book, Users, Tag } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { GraphNode } from "@/lib/types";

// Simple markdown to HTML converter for basic formatting
function markdownToHtml(text: string): string {
  return text
    // Headers
    .replace(/^## (.*$)/gm, '<h3 class="text-lg font-semibold text-linear-text-primary mb-3 mt-4 first:mt-0 font-inter">$1</h3>')
    // Bold text
    .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold text-linear-text-primary">$1</strong>')
    // List items
    .replace(/^- \*\*(.*?)\*\*：(.*$)/gm, '<div class="mb-2"><strong class="text-linear-text-primary">$1</strong>：$2</div>')
    // Line breaks
    .replace(/\n\n/g, '<br><br>')
    .replace(/\n/g, '<br>');
}

interface ConceptSidebarProps {
  node: GraphNode | null;
  isOpen: boolean;
  onClose: () => void;
  onConceptClick: (concept: string) => void;
}

export function ConceptSidebar({ node, isOpen, onClose, onConceptClick }: ConceptSidebarProps) {
  if (!node) return null;

  const categoryColors = {
    core: "bg-philo-primary",
    related: "bg-philo-secondary",
    principle: "bg-philo-accent"
  };

  const categoryIcons = {
    core: Users,
    related: Tag,
    principle: Book
  };

  const IconComponent = categoryIcons[node.category] || Users;

  return (
    <div 
      className={`fixed top-16 right-0 w-96 h-screen bg-linear-surface floating-sidebar shadow-linear-xl transform transition-transform duration-300 z-30 ${
        isOpen ? 'translate-x-0' : 'translate-x-full'
      } md:w-96`}
      style={{ width: window.innerWidth < 768 ? '100vw' : '24rem' }}
    >
      <ScrollArea className="h-full">
        <div className="p-6">
          {/* Close Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="absolute top-4 right-4 p-2 hover:bg-linear-surface-secondary rounded-full transition-colors"
          >
            <X className="w-4 h-4 text-linear-text-secondary" />
          </Button>
          
          {/* Content Area */}
          <div className="mt-8">
            {/* Header */}
            <div className="mb-6">
              <div className={`w-12 h-12 ${categoryColors[node.category]} rounded-xl flex items-center justify-center mb-4 shadow-linear-sm`}>
                <IconComponent className="w-6 h-6 text-white" />
              </div>
              <h3 className="font-inter text-2xl font-semibold text-linear-text-primary mb-2 tracking-tight">
                {node.name}
              </h3>
              <Badge variant="secondary" className="text-linear-text-secondary font-inter text-sm bg-linear-surface-secondary border-linear-border">
                {node.category === 'core' ? '核心概念' : 
                 node.category === 'related' ? '相关思想' : '关键原理'}
              </Badge>
            </div>
            
            <div className="space-y-6">
              {/* Explanation */}
              <section>
                <h4 className="font-inter font-semibold text-linear-text-primary mb-3">解释</h4>
                <div className="text-linear-text-secondary font-inter text-sm leading-relaxed">
                  <div dangerouslySetInnerHTML={{ __html: markdownToHtml(node.definition) }} />
                </div>
              </section>
              
              {/* Key Philosophers */}
              {node.keyPhilosophers.length > 0 && (
                <section>
                  <h4 className="font-inter font-semibold text-linear-text-primary mb-4 flex items-center">
                    <Users className="w-4 h-4 mr-2" />
                    主要哲学家
                  </h4>
                  <div className="space-y-3">
                    {node.keyPhilosophers.map((philosopher, index) => (
                      <div key={index} className="flex items-start space-x-3 p-4 bg-linear-surface-secondary rounded-lg border border-linear-border-light">
                        <div className={`w-8 h-8 ${categoryColors[node.category]} rounded-lg flex items-center justify-center flex-shrink-0 shadow-linear-sm`}>
                          <span className="text-white text-xs font-bold">
                            {philosopher.initials}
                          </span>
                        </div>
                        <div>
                          <h5 className="font-medium text-linear-text-primary font-inter">{philosopher.name}</h5>
                          <p className="text-xs text-linear-text-secondary font-inter">{philosopher.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </section>
              )}
              
              {/* Key Works */}
              {node.keyWorks.length > 0 && (
                <section>
                  <h4 className="font-inter font-semibold text-linear-text-primary mb-4 flex items-center">
                    <Book className="w-4 h-4 mr-2" />
                    重要著作
                  </h4>
                  <div className="space-y-2">
                    {node.keyWorks.map((work, index) => (
                      <div 
                        key={index}
                        className="flex items-center space-x-3 p-3 hover:bg-linear-surface-secondary rounded-lg transition-colors cursor-pointer"
                      >
                        <Book className={`w-4 h-4 ${
                          node.category === 'core' ? 'text-philo-primary' : 
                          node.category === 'related' ? 'text-philo-secondary' : 
                          'text-philo-accent'
                        }`} />
                        <span className="text-sm text-linear-text-primary font-inter">{work}</span>
                      </div>
                    ))}
                  </div>
                </section>
              )}
              
              {/* Related Concepts */}
              {node.relatedConcepts.length > 0 && (
                <section>
                  <h4 className="font-inter font-semibold text-linear-text-primary mb-4 flex items-center">
                    <Tag className="w-4 h-4 mr-2" />
                    相关概念
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {node.relatedConcepts.map((concept, index) => (
                      <Badge
                        key={index}
                        variant="outline"
                        className={`px-3 py-1 text-xs font-inter cursor-pointer transition-all hover:shadow-linear-sm ${
                          node.category === 'core' ? 'text-philo-primary border-philo-primary/20 hover:bg-philo-primary/10' :
                          node.category === 'related' ? 'text-philo-secondary border-philo-secondary/20 hover:bg-philo-secondary/10' :
                          'text-philo-accent border-philo-accent/20 hover:bg-philo-accent/10'
                        }`}
                        onClick={() => onConceptClick(concept)}
                      >
                        {concept}
                      </Badge>
                    ))}
                  </div>
                </section>
              )}
            </div>
          </div>
        </div>
      </ScrollArea>
    </div>
  );
}
