@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
    /* Linear-inspired design system */
    --background: hsl(0, 0%, 100%);
    --foreground: hsl(204, 10%, 10%);
    --muted: hsl(210, 15%, 97%);
    --muted-foreground: hsl(204, 6%, 56%);
    --popover: hsl(0, 0%, 100%);
    --popover-foreground: hsl(204, 10%, 10%);
    --card: hsl(0, 0%, 100%);
    --card-foreground: hsl(204, 10%, 10%);
    --border: hsl(204, 7%, 89%);
    --input: hsl(204, 7%, 89%);
    --primary: hsl(240, 90%, 58%);
    --primary-foreground: hsl(0, 0%, 100%);
    --secondary: hsl(210, 15%, 97%);
    --secondary-foreground: hsl(204, 10%, 10%);
    --accent: hsl(210, 15%, 97%);
    --accent-foreground: hsl(204, 10%, 10%);
    --destructive: hsl(0, 84%, 60%);
    --destructive-foreground: hsl(0, 0%, 100%);
    --ring: hsl(240, 90%, 58%);
    --radius: 0.5rem;

    /* Linear-inspired colors */
    --linear-primary: hsl(240, 90%, 58%);
    --linear-secondary: hsl(204, 10%, 10%);
    --linear-accent: hsl(260, 70%, 65%);
    --linear-surface: hsl(0, 0%, 100%);
    --linear-surface-secondary: hsl(210, 15%, 97%);
    --linear-surface-tertiary: hsl(204, 7%, 89%);
    --linear-text-primary: hsl(204, 10%, 10%);
    --linear-text-secondary: hsl(204, 6%, 56%);
    --linear-text-tertiary: hsl(204, 4%, 74%);
    --linear-border: hsl(204, 7%, 89%);
    --linear-border-light: hsl(204, 7%, 95%);

    /* Philosophy-specific colors updated for Linear style */
    --philo-primary: hsl(240, 90%, 58%);
    --philo-secondary: hsl(260, 70%, 65%);
    --philo-accent: hsl(200, 80%, 55%);
    --philo-surface: hsl(0, 0%, 100%);
    --philo-text: hsl(204, 10%, 10%);
    --philo-text-muted: hsl(204, 6%, 56%);
}

.dark {
    --background: hsl(222.2, 84%, 4.9%);
    --foreground: hsl(210, 40%, 98%);
    --muted: hsl(217.2, 32.6%, 17.5%);
    --muted-foreground: hsl(215, 20.2%, 65.1%);
    --popover: hsl(222.2, 84%, 4.9%);
    --popover-foreground: hsl(210, 40%, 98%);
    --card: hsl(222.2, 84%, 4.9%);
    --card-foreground: hsl(210, 40%, 98%);
    --border: hsl(217.2, 32.6%, 17.5%);
    --input: hsl(217.2, 32.6%, 17.5%);
    --primary: hsl(207, 90%, 54%);
    --primary-foreground: hsl(210, 40%, 98%);
    --secondary: hsl(217.2, 32.6%, 17.5%);
    --secondary-foreground: hsl(210, 40%, 98%);
    --accent: hsl(217.2, 32.6%, 17.5%);
    --accent-foreground: hsl(210, 40%, 98%);
    --destructive: hsl(0, 62.8%, 30.6%);
    --destructive-foreground: hsl(210, 40%, 98%);
    --ring: hsl(212.7, 26.8%, 83.9%);
}

@layer base {
    * {
        @apply border-border;
    }

    body {
        @apply font-sans antialiased bg-background text-foreground;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
        line-height: 1.5;
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        font-weight: 600;
        line-height: 1.2;
        letter-spacing: -0.02em;
    }

    .font-inter {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    }
}

@layer utilities {

    /* Linear-inspired utilities */
    .text-linear-primary {
        color: var(--linear-primary);
    }

    .text-linear-secondary {
        color: var(--linear-secondary);
    }

    .text-linear-accent {
        color: var(--linear-accent);
    }

    .text-linear-text-primary {
        color: var(--linear-text-primary);
    }

    .text-linear-text-secondary {
        color: var(--linear-text-secondary);
    }

    .text-linear-text-tertiary {
        color: var(--linear-text-tertiary);
    }

    .bg-linear-primary {
        background-color: var(--linear-primary);
    }

    .bg-linear-secondary {
        background-color: var(--linear-secondary);
    }

    .bg-linear-accent {
        background-color: var(--linear-accent);
    }

    .bg-linear-surface {
        background-color: var(--linear-surface);
    }

    .bg-linear-surface-secondary {
        background-color: var(--linear-surface-secondary);
    }

    .bg-linear-surface-tertiary {
        background-color: var(--linear-surface-tertiary);
    }

    .border-linear-border {
        border-color: var(--linear-border);
    }

    .border-linear-border-light {
        border-color: var(--linear-border-light);
    }

    /* Philosophy-specific utilities */
    .text-philo-primary {
        color: var(--philo-primary);
    }

    .text-philo-secondary {
        color: var(--philo-secondary);
    }

    .text-philo-accent {
        color: var(--philo-accent);
    }

    .text-philo-text {
        color: var(--philo-text);
    }

    .text-philo-text-muted {
        color: var(--philo-text-muted);
    }

    .bg-philo-primary {
        background-color: var(--philo-primary);
    }

    .bg-philo-secondary {
        background-color: var(--philo-secondary);
    }

    .bg-philo-accent {
        background-color: var(--philo-accent);
    }

    .bg-philo-surface {
        background-color: var(--philo-surface);
    }

    .border-philo-primary {
        border-color: var(--philo-primary);
    }

    .border-philo-secondary {
        border-color: var(--philo-secondary);
    }

    .border-philo-accent {
        border-color: var(--philo-accent);
    }
}

/* Graph-specific styles */
.graph-node {
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.graph-link {
    stroke-dasharray: 5, 5;
}

.floating-sidebar {
    backdrop-filter: blur(24px);
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid var(--linear-border-light);
}

/* Linear-inspired shadows */
.shadow-linear-sm {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08), 0 1px 2px rgba(0, 0, 0, 0.02);
}

.shadow-linear-md {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.03);
}

.shadow-linear-lg {
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
}

.shadow-linear-xl {
    box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);
}

/* Custom animations with Linear timing */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(4px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
    }

    to {
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }

    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes pulseSubtle {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.7;
    }
}

.animate-fade-in {
    animation: fadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-slide-in {
    animation: slideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-scale-in {
    animation: scaleIn 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-pulse-subtle {
    animation: pulseSubtle 2s infinite;
}

/* Button variations */
.btn-primary-linear {
    background: var(--linear-primary);
    color: white;
    border: none;
    border-radius: 0.375rem;
    padding: 0.5rem 1rem;
    font-weight: 500;
    font-size: 0.875rem;
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-primary-linear:hover {
    background: hsl(240, 90%, 52%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(79, 70, 229, 0.3);
}

.btn-secondary-linear {
    background: var(--linear-surface-secondary);
    color: var(--linear-text-primary);
    border: 1px solid var(--linear-border);
    border-radius: 0.375rem;
    padding: 0.5rem 1rem;
    font-weight: 500;
    font-size: 0.875rem;
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-secondary-linear:hover {
    background: var(--linear-surface-tertiary);
    border-color: var(--linear-border);
    transform: translateY(-1px);
}

/* Input styles */
.input-linear {
    background: var(--linear-surface);
    border: 1px solid var(--linear-border);
    border-radius: 0.375rem;
    padding: 0.625rem 0.75rem;
    font-size: 0.875rem;
    color: var(--linear-text-primary);
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

.input-linear:focus {
    outline: none;
    border-color: var(--linear-primary);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* Touch-friendly styles for mobile */
@media (max-width: 768px) {
    .graph-node {
        touch-action: manipulation;
    }

    .floating-sidebar {
        width: 100vw !important;
    }
}