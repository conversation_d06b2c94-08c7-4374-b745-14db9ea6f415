import { useState } from "react";
import { <PERSON>u, Info, Home as HomeIcon } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { SearchInterface } from "@/components/search-interface";
import { KnowledgeGraph } from "@/components/knowledge-graph";
import { ConceptSidebar } from "@/components/concept-sidebar";
import { useGenerateGraph } from "@/hooks/use-philosophy-graph";
import { GraphNode, KnowledgeGraph as KnowledgeGraphType } from "@/lib/types";

export default function Home() {
  const [currentGraph, setCurrentGraph] = useState<KnowledgeGraphType | null>(null);
  const [selectedNode, setSelectedNode] = useState<GraphNode | null>(null);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { toast } = useToast();

  const generateGraphMutation = useGenerateGraph();

  const handleSearch = async (topic: string) => {
    try {
      const graph = await generateGraphMutation.mutateAsync(topic);
      setCurrentGraph(graph);
      setSelectedNode(null);
      setSidebarOpen(false);
      
      toast({
        title: "知识图谱已生成",
        description: `成功为"${topic}"创建了知识图谱`,
      });
    } catch (error) {
      console.error("Failed to generate graph:", error);
      toast({
        title: "错误",
        description: "生成知识图谱失败，请重试。",
        variant: "destructive",
      });
    }
  };

  const handleNodeClick = (node: GraphNode) => {
    setSelectedNode(node);
    setSidebarOpen(true);
  };

  const handleConceptClick = (conceptName: string) => {
    // Find the concept in current graph nodes
    const node = currentGraph?.nodes.find(n => 
      n.name.toLowerCase() === conceptName.toLowerCase()
    );
    
    if (node) {
      setSelectedNode(node);
    } else {
      // If concept not found in current graph, search for it
      handleSearch(conceptName);
      setSidebarOpen(false);
    }
  };

  const handleCloseSidebar = () => {
    setSidebarOpen(false);
  };

  return (
    <div className="min-h-screen bg-linear-surface">
      {/* Header */}
      <header className="bg-linear-surface shadow-linear-sm border-b border-linear-border-light sticky top-0 z-50 backdrop-blur-md">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-philo-primary to-philo-secondary rounded-lg flex items-center justify-center shadow-linear-sm">
                <svg
                  className="w-6 h-6 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13 10V3L4 14h7v7l9-11h-7z"
                  />
                </svg>
              </div>
              <div>
                <h1 className="font-inter text-xl font-semibold text-linear-text-primary">哲学图谱</h1>
                <p className="text-xs text-linear-text-tertiary font-inter">交互式哲学探索器</p>
              </div>
            </div>
            
            <nav className="hidden md:flex items-center space-x-6">
              <a href="#" className="text-linear-text-secondary hover:text-linear-text-primary transition-colors font-inter text-sm font-medium">
                探索
              </a>
              <a href="#" className="text-linear-text-secondary hover:text-linear-text-primary transition-colors font-inter text-sm font-medium">
                图书馆
              </a>
              <a href="#" className="text-linear-text-secondary hover:text-linear-text-primary transition-colors font-inter text-sm font-medium">
                关于
              </a>
              <Button className="btn-primary-linear">
                登录
              </Button>
            </nav>
            
            <Button variant="ghost" size="sm" className="md:hidden p-2">
              <Menu className="w-5 h-5 text-linear-text-primary" />
            </Button>
          </div>
        </div>
      </header>

      {/* Search Interface */}
      <SearchInterface 
        onSearch={handleSearch} 
        isLoading={generateGraphMutation.isPending}
      />

      {/* Graph Canvas */}
      <KnowledgeGraph
        nodes={currentGraph?.nodes || []}
        links={currentGraph?.links || []}
        onNodeClick={handleNodeClick}
        isLoading={generateGraphMutation.isPending}
      />

      {/* Concept Sidebar */}
      <ConceptSidebar
        node={selectedNode}
        isOpen={sidebarOpen}
        onClose={handleCloseSidebar}
        onConceptClick={handleConceptClick}
      />

      {/* Mobile Controls */}
      <div className="fixed bottom-4 right-4 md:hidden z-20">
        <div className="bg-linear-surface rounded-full shadow-linear-lg p-3 flex items-center space-x-3 border border-linear-border-light">
          <Button
            size="sm"
            onClick={() => setSidebarOpen(!sidebarOpen)}
            className="p-2 bg-philo-primary text-white rounded-full hover:bg-philo-primary/90 transition-all shadow-linear-sm"
            disabled={!selectedNode}
          >
            <Info className="w-4 h-4" />
          </Button>
          <Button
            size="sm"
            onClick={() => {
              setCurrentGraph(null);
              setSelectedNode(null);
              setSidebarOpen(false);
            }}
            className="p-2 bg-philo-accent text-white rounded-full hover:bg-philo-accent/90 transition-all shadow-linear-sm"
          >
            <HomeIcon className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
